<?php

namespace app\api\controller;

use app\BaseController;
use app\api\model\Union as UnionModel;
use app\api\service\JwtService;
use think\facade\Cache;
use think\facade\Request;


class Employee extends BaseController
{

    protected bool $checkPassword = true;

    /**
     * 登录
     */
    public function login()
    {
        if ($this->checkPassword) {
            $admin = UnionModel::where('telephone',$this->request->param('telephone'))->whereRaw("password = SHA1(CONCAT(salt, SHA1(?)))", [$this->request->param('password')])->find();
        } else {
            $admin = UnionModel::where('telephone',$this->request->param('telephone'))->find();
        }

        if (!$admin) {
            return $this->error('账号或密码错误');
        }

        // 生成Token
        $token = JwtService::generateToken($admin->toArray()['union_id']);

        return $this->success(['token' => $token]);

    }

    /**
     * 退出登录
     * @return \think\response\Json
     */
    public function logout()
    {
        $token = Request::header('authorization',
            $_SERVER['HTTP_AUTHORIZATION'] ??
            $_SERVER['REDIRECT_HTTP_AUTHORIZATION'] ?? '');

        // 去除Bearer前缀
        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }
        $decoded = JwtService::verifyToken($token);
        $expireTime = $decoded['exp'] - time();
        Cache::set('jwt:blacklist:'.$token, 1, $expireTime);

        return $this->success(['clear_token' => true]);
    }

    /**
     * 获取当前用户信息
     * @return \think\response\Json
     */
    public function info()
    {
        $union_info = $this->request->unionInfo;

        return $this->success($union_info);
    }
}
